# =============================================================================
# SNMP-Modbus 桥接服务配置文件 (Linux 版本)
# =============================================================================

[SNMP_BRIDGE_CONFIG]
listen_ip = 0.0.0.0
listen_port = 161
community = public
modbus_type = TCP
timezone_offset = +08
startup_delay = 2
error_value = -99998

[MODBUS_TCP_CONFIG]
server_ip = *************
port = 502
timeout = 3
retry_interval = 10
update_interval = 5

[MODBUS_RTU_CONFIG]
port = /dev/ttyUSB0
baudrate = 9600
bytesize = 8
parity = N
stopbits = 1
timeout = 3
retry_interval = 10
update_interval = 5

[SYSTEM_OID_1]
oid = .*******.*******.0
description = System Description
type = fixed_value
value = SNMP-Modbus Bridge v1.0 (Linux)
snmp_data_type = OctetString

[SYSTEM_OID_2]
oid = .*******.4.1.41475.********.1.0
description = udSystemName
type = fixed_value
value = T-Block-UD
snmp_data_type = OctetString

[SYSTEM_OID_3]
oid = .*******.4.1.41475.********.2.0
description = udSystemDesc
type = fixed_value
value = Industrial SNMP-Modbus Gateway
snmp_data_type = OctetString

[SYSTEM_OID_4]
oid = .*******.4.1.41475.********.3.0
description = udSystemManufacturer
type = fixed_value
value = Industrial Automation Co.
snmp_data_type = OctetString

[SYSTEM_OID_5]
oid = .*******.4.1.41475.********.4.0
description = udSystemTime
type = utc_time
snmp_data_type = OctetString

[SYSTEM_OID_6]
oid = .*******.4.1.41475.********.*******
description = udEquipIndex
type = fixed_value
value = 1
snmp_data_type = Integer

[SNMP_OID_1]
oid = .*******.4.1.41475.********.*******
description = temperature_sensor_1
register_address = 0x100
unit_id = 1
function_code = 3
data_type = int16
processing_type = multiply
coefficient = 0.1
offset = 0
decimal_places = 1
snmp_data_type = OctetString

[SNMP_OID_2]
oid = .*******.4.1.41475.********.*******
description = humidity_sensor_1
register_address = 0x101
unit_id = 1
function_code = 3
data_type = uint16
processing_type = multiply
coefficient = 0.01
offset = 0
decimal_places = 2
snmp_data_type = OctetString

[SNMP_OID_3]
oid = .*******.4.1.41475.*********.********.0
description = device_operation_mode
register_address = 0x200
unit_id = 1
function_code = 3
data_type = uint16
processing_type = direct
snmp_data_type = Integer32

[SNMP_OID_4]
oid = .*******.4.1.41475.*********.*******
description = communication_status
processing_type = communication_status
snmp_data_type = Integer32
