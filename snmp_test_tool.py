#!/usr/bin/env python3
"""
SNMP 测试工具

简化的命令行工具，用于测试 SNMP walk、bulk 和 subtree 功能
"""

import sys
import argparse

# SNMP 相关导入
from pysnmp.hlapi import *

def snmp_get(host, port, community, oid):
    """执行 SNMP GET 操作"""
    print(f"🔍 SNMP GET: {oid}")
    
    for (errorIndication, errorStatus, errorIndex, varBinds) in getCmd(
        SnmpEngine(),
        CommunityData(community),
        UdpTransportTarget((host, port)),
        ContextData(),
        ObjectType(ObjectIdentity(oid))
    ):
        
        if errorIndication:
            print(f"❌ 错误: {errorIndication}")
            return False
        elif errorStatus:
            print(f"❌ 错误: {errorStatus.prettyPrint()}")
            return False
        else:
            for varBind in varBinds:
                print(f"✅ {varBind[0]} = {varBind[1]}")
            return True

def snmp_getnext(host, port, community, oid, count=5):
    """执行 SNMP GETNEXT 操作"""
    print(f"🔍 SNMP GETNEXT: {oid} (最多 {count} 个)")
    
    current_oid = oid
    results = 0
    
    for i in range(count):
        for (errorIndication, errorStatus, errorIndex, varBinds) in nextCmd(
            SnmpEngine(),
            CommunityData(community),
            UdpTransportTarget((host, port)),
            ContextData(),
            ObjectType(ObjectIdentity(current_oid)),
            lexicographicMode=False,
            maxRows=1
        ):
            
            if errorIndication:
                print(f"❌ 错误: {errorIndication}")
                return results
            elif errorStatus:
                print(f"❌ 错误: {errorStatus.prettyPrint()}")
                return results
            else:
                for varBind in varBinds:
                    print(f"✅ {varBind[0]} = {varBind[1]}")
                    current_oid = str(varBind[0])
                    results += 1
                break
    
    return results

def snmp_bulk(host, port, community, oid, non_repeaters=0, max_repetitions=5):
    """执行 SNMP GETBULK 操作"""
    print(f"🔍 SNMP GETBULK: {oid} (non_repeaters={non_repeaters}, max_repetitions={max_repetitions})")
    
    results = 0
    
    for (errorIndication, errorStatus, errorIndex, varBinds) in bulkCmd(
        SnmpEngine(),
        CommunityData(community),
        UdpTransportTarget((host, port)),
        ContextData(),
        non_repeaters,
        max_repetitions,
        ObjectType(ObjectIdentity(oid)),
        lexicographicMode=False,
        maxRows=max_repetitions
    ):
        
        if errorIndication:
            print(f"❌ 错误: {errorIndication}")
            return results
        elif errorStatus:
            print(f"❌ 错误: {errorStatus.prettyPrint()}")
            return results
        else:
            for varBind in varBinds:
                print(f"✅ {varBind[0]} = {varBind[1]}")
                results += 1
            break
    
    return results

def snmp_walk(host, port, community, oid, max_results=20):
    """执行 SNMP WALK 操作"""
    print(f"🔍 SNMP WALK: {oid} (最多 {max_results} 个)")
    
    results = 0
    
    for (errorIndication, errorStatus, errorIndex, varBinds) in nextCmd(
        SnmpEngine(),
        CommunityData(community),
        UdpTransportTarget((host, port)),
        ContextData(),
        ObjectType(ObjectIdentity(oid)),
        lexicographicMode=False,
        maxRows=max_results
    ):
        
        if errorIndication:
            print(f"❌ 错误: {errorIndication}")
            break
        elif errorStatus:
            print(f"❌ 错误: {errorStatus.prettyPrint()}")
            break
        else:
            for varBind in varBinds:
                # 检查是否仍在指定子树内
                oid_str = str(varBind[0])
                if oid_str.startswith(oid):
                    print(f"✅ {varBind[0]} = {varBind[1]}")
                    results += 1
                else:
                    print(f"🏁 已离开子树: {varBind[0]}")
                    return results
    
    return results

def snmp_subtree(host, port, community, oid, max_results=50):
    """执行 SNMP SUBTREE 操作（使用 WALK 实现）"""
    print(f"🔍 SNMP SUBTREE: {oid} (最多 {max_results} 个)")
    
    return snmp_walk(host, port, community, oid, max_results)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='SNMP 测试工具')
    parser.add_argument('command', choices=['get', 'getnext', 'bulk', 'walk', 'subtree'], 
                       help='SNMP 命令')
    parser.add_argument('oid', help='目标 OID')
    parser.add_argument('--host', default='127.0.0.1', help='SNMP 主机地址 (默认: 127.0.0.1)')
    parser.add_argument('--port', type=int, default=1161, help='SNMP 端口 (默认: 1161)')
    parser.add_argument('--community', default='public', help='SNMP 团体名 (默认: public)')
    parser.add_argument('--count', type=int, default=5, help='GETNEXT 返回数量 (默认: 5)')
    parser.add_argument('--non-repeaters', type=int, default=0, help='BULK non_repeaters (默认: 0)')
    parser.add_argument('--max-repetitions', type=int, default=5, help='BULK max_repetitions (默认: 5)')
    parser.add_argument('--max-results', type=int, default=20, help='WALK/SUBTREE 最大结果数 (默认: 20)')
    
    args = parser.parse_args()
    
    print(f"🚀 SNMP 测试工具")
    print(f"📡 目标: {args.host}:{args.port}")
    print(f"🔑 团体名: {args.community}")
    print("=" * 50)
    
    try:
        if args.command == 'get':
            result = snmp_get(args.host, args.port, args.community, args.oid)
        elif args.command == 'getnext':
            result = snmp_getnext(args.host, args.port, args.community, args.oid, args.count)
        elif args.command == 'bulk':
            result = snmp_bulk(args.host, args.port, args.community, args.oid, 
                             args.non_repeaters, args.max_repetitions)
        elif args.command == 'walk':
            result = snmp_walk(args.host, args.port, args.community, args.oid, args.max_results)
        elif args.command == 'subtree':
            result = snmp_subtree(args.host, args.port, args.community, args.oid, args.max_results)
        
        print("=" * 50)
        if isinstance(result, bool):
            if result:
                print("✅ 操作成功")
            else:
                print("❌ 操作失败")
        else:
            print(f"📊 返回了 {result} 个结果")
        
        return result
        
    except Exception as e:
        print(f"❌ 异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
