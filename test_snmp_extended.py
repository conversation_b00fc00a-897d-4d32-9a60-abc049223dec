#!/usr/bin/env python3
"""
SNMP 扩展功能测试脚本

测试新增的 SNMP walk、bulk 和 subtree 功能
包括单元测试和集成测试
"""

import sys
import time
import subprocess
import logging

# SNMP 相关导入
from pysnmp.hlapi import *

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SNMPExtendedTester:
    """SNMP 扩展功能测试器"""
    
    def __init__(self, host='127.0.0.1', port=1161, community='public'):
        self.host = host
        self.port = port
        self.community = community
        self.bridge_process = None
        
    def start_bridge_service(self):
        """启动桥接服务（用于测试）"""
        try:
            logger.info("🚀 启动 SNMP-Modbus 桥接服务...")
            self.bridge_process = subprocess.Popen(
                [sys.executable, 'snmp_modbus_bridge.py'],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            # 等待服务启动
            time.sleep(3)
            logger.info("✅ 桥接服务已启动")
            return True
        except Exception as e:
            logger.error(f"❌ 启动桥接服务失败: {e}")
            return False
    
    def stop_bridge_service(self):
        """停止桥接服务"""
        if self.bridge_process:
            logger.info("🛑 停止桥接服务...")
            self.bridge_process.terminate()
            self.bridge_process.wait()
            logger.info("✅ 桥接服务已停止")
    
    def test_snmp_get(self):
        """测试基本的 SNMP GET 功能"""
        logger.info("\n🧪 测试 SNMP GET 功能")
        logger.info("=" * 50)
        
        test_oids = [
            '.*******.*******.0',  # 系统描述
            '.*******.4.1.41475.********.4.0',  # 系统时间
        ]
        
        success_count = 0
        for oid in test_oids:
            try:
                logger.info(f"📍 测试 OID: {oid}")
                
                for (errorIndication, errorStatus, errorIndex, varBinds) in getCmd(
                    SnmpEngine(),
                    CommunityData(self.community),
                    UdpTransportTarget((self.host, self.port)),
                    ContextData(),
                    ObjectType(ObjectIdentity(oid))
                ):
                    
                    if errorIndication:
                        logger.error(f"❌ 错误: {errorIndication}")
                    elif errorStatus:
                        logger.error(f"❌ 错误: {errorStatus.prettyPrint()}")
                    else:
                        for varBind in varBinds:
                            logger.info(f"✅ {varBind[0]} = {varBind[1]}")
                            success_count += 1
                        
            except Exception as e:
                logger.error(f"❌ GET 测试异常: {e}")
        
        logger.info(f"📊 GET 测试结果: {success_count}/{len(test_oids)} 成功")
        return success_count == len(test_oids)
    
    def test_snmp_getnext(self):
        """测试 SNMP GETNEXT 功能"""
        logger.info("\n🧪 测试 SNMP GETNEXT 功能")
        logger.info("=" * 50)
        
        start_oid = '.*******.2.1.1'
        max_iterations = 5
        
        try:
            logger.info(f"📍 从 OID {start_oid} 开始 GETNEXT")
            
            current_oid = start_oid
            success_count = 0
            
            for i in range(max_iterations):
                for (errorIndication, errorStatus, errorIndex, varBinds) in nextCmd(
                    SnmpEngine(),
                    CommunityData(self.community),
                    UdpTransportTarget((self.host, self.port)),
                    ContextData(),
                    ObjectType(ObjectIdentity(current_oid)),
                    lexicographicMode=False,
                    maxRows=1
                ):
                    
                    if errorIndication:
                        logger.error(f"❌ 错误: {errorIndication}")
                        break
                    elif errorStatus:
                        logger.error(f"❌ 错误: {errorStatus.prettyPrint()}")
                        break
                    else:
                        for varBind in varBinds:
                            logger.info(f"✅ {varBind[0]} = {varBind[1]}")
                            current_oid = str(varBind[0])
                            success_count += 1
                        break
            
            logger.info(f"📊 GETNEXT 测试结果: 获取了 {success_count} 个 OID")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"❌ GETNEXT 测试异常: {e}")
            return False
    
    def test_snmp_bulk(self):
        """测试 SNMP GETBULK 功能"""
        logger.info("\n🧪 测试 SNMP GETBULK 功能")
        logger.info("=" * 50)
        
        start_oid = '.*******.2.1.1'
        non_repeaters = 0
        max_repetitions = 5
        
        try:
            logger.info(f"📍 BULK 查询: OID={start_oid}, non_repeaters={non_repeaters}, max_repetitions={max_repetitions}")
            
            success_count = 0
            
            for (errorIndication, errorStatus, errorIndex, varBinds) in bulkCmd(
                SnmpEngine(),
                CommunityData(self.community),
                UdpTransportTarget((self.host, self.port)),
                ContextData(),
                non_repeaters,
                max_repetitions,
                ObjectType(ObjectIdentity(start_oid)),
                lexicographicMode=False,
                maxRows=max_repetitions
            ):
                
                if errorIndication:
                    logger.error(f"❌ 错误: {errorIndication}")
                    break
                elif errorStatus:
                    logger.error(f"❌ 错误: {errorStatus.prettyPrint()}")
                    break
                else:
                    for varBind in varBinds:
                        logger.info(f"✅ {varBind[0]} = {varBind[1]}")
                        success_count += 1
                    break
            
            logger.info(f"📊 BULK 测试结果: 获取了 {success_count} 个 OID")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"❌ BULK 测试异常: {e}")
            return False
    
    def test_snmp_walk(self):
        """测试 SNMP WALK 功能"""
        logger.info("\n🧪 测试 SNMP WALK 功能")
        logger.info("=" * 50)
        
        start_oid = '.*******.2.1.1'
        max_results = 10
        
        try:
            logger.info(f"📍 WALK 查询: 从 OID {start_oid} 开始")
            
            success_count = 0
            
            for (errorIndication, errorStatus, errorIndex, varBinds) in nextCmd(
                SnmpEngine(),
                CommunityData(self.community),
                UdpTransportTarget((self.host, self.port)),
                ContextData(),
                ObjectType(ObjectIdentity(start_oid)),
                lexicographicMode=False,
                maxRows=max_results
            ):
                
                if errorIndication:
                    logger.error(f"❌ 错误: {errorIndication}")
                    break
                elif errorStatus:
                    logger.error(f"❌ 错误: {errorStatus.prettyPrint()}")
                    break
                else:
                    for varBind in varBinds:
                        # 检查是否仍在指定子树内
                        oid_str = str(varBind[0])
                        if oid_str.startswith(start_oid):
                            logger.info(f"✅ {varBind[0]} = {varBind[1]}")
                            success_count += 1
                        else:
                            logger.info(f"🏁 已离开子树: {varBind[0]}")
                            break
            
            logger.info(f"📊 WALK 测试结果: 遍历了 {success_count} 个 OID")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"❌ WALK 测试异常: {e}")
            return False
    
    def test_performance(self):
        """性能测试"""
        logger.info("\n🧪 性能测试")
        logger.info("=" * 50)
        
        test_oid = '.*******.*******.0'
        iterations = 10
        
        try:
            logger.info(f"📍 性能测试: {iterations} 次 GET 请求")
            
            start_time = time.time()
            success_count = 0
            
            for i in range(iterations):
                for (errorIndication, errorStatus, errorIndex, varBinds) in getCmd(
                    SnmpEngine(),
                    CommunityData(self.community),
                    UdpTransportTarget((self.host, self.port)),
                    ContextData(),
                    ObjectType(ObjectIdentity(test_oid))
                ):
                    
                    if not errorIndication and not errorStatus:
                        success_count += 1
                    break
            
            end_time = time.time()
            total_time = end_time - start_time
            avg_time = total_time / iterations if iterations > 0 else 0
            
            logger.info(f"📊 性能测试结果:")
            logger.info(f"   总时间: {total_time:.3f} 秒")
            logger.info(f"   平均响应时间: {avg_time:.3f} 秒")
            logger.info(f"   成功率: {success_count}/{iterations} ({success_count/iterations*100:.1f}%)")
            
            return success_count == iterations
            
        except Exception as e:
            logger.error(f"❌ 性能测试异常: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始 SNMP 扩展功能测试")
        logger.info("=" * 60)
        
        tests = [
            ("SNMP GET", self.test_snmp_get),
            ("SNMP GETNEXT", self.test_snmp_getnext),
            ("SNMP BULK", self.test_snmp_bulk),
            ("SNMP WALK", self.test_snmp_walk),
            ("性能测试", self.test_performance)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            try:
                logger.info(f"\n🔍 开始测试: {test_name}")
                if test_func():
                    logger.info(f"✅ {test_name} 测试通过")
                    passed += 1
                else:
                    logger.error(f"❌ {test_name} 测试失败")
            except Exception as e:
                logger.error(f"❌ {test_name} 测试异常: {e}")
        
        logger.info("\n" + "=" * 60)
        logger.info(f"📊 测试结果: {passed}/{total} 项测试通过")
        
        if passed == total:
            logger.info("🎉 所有测试通过！SNMP 扩展功能正常工作")
        else:
            logger.warning("⚠️  部分测试失败，请检查上述错误信息")
        
        return passed == total


def main():
    """主函数"""
    tester = SNMPExtendedTester()
    
    try:
        # 启动桥接服务
        if not tester.start_bridge_service():
            logger.error("❌ 无法启动桥接服务，退出测试")
            return False
        
        # 运行测试
        success = tester.run_all_tests()
        
        return success
        
    finally:
        # 停止桥接服务
        tester.stop_bridge_service()


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
